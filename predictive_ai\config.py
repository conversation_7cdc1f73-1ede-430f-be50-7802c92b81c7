"""
Configuration module for the AI Data Science Pipeline
"""
import os
from dataclasses import dataclass
from typing import Dict, List, Optional
import yaml
from pathlib import Path

@dataclass
class DatabaseConfig:
    """PostgreSQL database configuration"""
    host: str = "localhost"
    port: int = 5432
    database: str = "predictive_ai"
    username: str = "postgres"
    password: str = ""

    @property
    def connection_string(self) -> str:
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

@dataclass
class CeleryConfig:
    """Celery configuration for task queue"""
    broker_url: str = "redis://localhost:6379/0"
    result_backend: str = "redis://localhost:6379/0"
    task_serializer: str = "json"
    accept_content: List[str] = None
    result_serializer: str = "json"
    timezone: str = "UTC"
    enable_utc: bool = True
    worker_concurrency: int = 3

    def __post_init__(self):
        if self.accept_content is None:
            self.accept_content = ['json']

@dataclass
class MLConfig:
    """Machine Learning configuration"""
    max_models_per_problem: int = 5
    default_test_size: float = 0.2
    random_state: int = 42
    cv_folds: int = 5
    hyperparameter_trials: int = 50
    model_timeout_seconds: int = 300

    # Model configurations by problem type
    classification_models: List[str] = None
    regression_models: List[str] = None
    clustering_models: List[str] = None

    def __post_init__(self):
        if self.classification_models is None:
            self.classification_models = [
                'RandomForestClassifier',
                'LogisticRegression',
                'XGBClassifier',
                'SVMClassifier',
                'GradientBoostingClassifier'
            ]
        if self.regression_models is None:
            self.regression_models = [
                'RandomForestRegressor',
                'LinearRegression',
                'XGBRegressor',
                'SVMRegressor',
                'GradientBoostingRegressor'
            ]
        if self.clustering_models is None:
            self.clustering_models = [
                'KMeans',
                'DBSCAN',
                'AgglomerativeClustering',
                'GaussianMixture'
            ]

@dataclass
class LLMConfig:
    """LLM configuration"""
    model_name: str = "gpt-3.5-turbo"
    api_key: str = ""
    max_tokens: int = 1000
    temperature: float = 0.7
    system_prompt: str = """You are an AI Data Science Assistant. You help users understand each step of the data science pipeline, explain recommendations, and provide insights about their data and models."""

@dataclass
class UIConfig:
    """UI configuration"""
    streamlit_port: int = 8501
    max_file_size_mb: int = 200
    allowed_file_types: List[str] = None

    def __post_init__(self):
        if self.allowed_file_types is None:
            self.allowed_file_types = ['.csv', '.xlsx', '.xls', '.txt', '.json']

class Config:
    """Main configuration class"""

    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or os.getenv('CONFIG_FILE', 'config.yaml')
        self.load_config()

    def load_config(self):
        """Load configuration from file and environment variables"""
        # Load from YAML file if exists
        config_data = {}
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r') as f:
                config_data = yaml.safe_load(f) or {}

        # Database configuration
        db_config = config_data.get('database', {})
        self.database = DatabaseConfig(
            host=os.getenv('DB_HOST', db_config.get('host', 'localhost')),
            port=int(os.getenv('DB_PORT', db_config.get('port', 5432))),
            database=os.getenv('DB_NAME', db_config.get('database', 'predictive_ai')),
            username=os.getenv('DB_USER', db_config.get('username', 'postgres')),
            password=os.getenv('DB_PASSWORD', db_config.get('password', ''))
        )

        # Celery configuration
        celery_config = config_data.get('celery', {})
        self.celery = CeleryConfig(
            broker_url=os.getenv('CELERY_BROKER_URL', celery_config.get('broker_url', 'redis://localhost:6379/0')),
            result_backend=os.getenv('CELERY_RESULT_BACKEND', celery_config.get('result_backend', 'redis://localhost:6379/0')),
            worker_concurrency=int(os.getenv('CELERY_CONCURRENCY', celery_config.get('worker_concurrency', 3)))
        )

        # ML configuration
        ml_config = config_data.get('ml', {})
        self.ml = MLConfig(
            max_models_per_problem=int(os.getenv('MAX_MODELS', ml_config.get('max_models_per_problem', 5))),
            default_test_size=float(os.getenv('TEST_SIZE', ml_config.get('default_test_size', 0.2))),
            random_state=int(os.getenv('RANDOM_STATE', ml_config.get('random_state', 42)))
        )

        # LLM configuration
        llm_config = config_data.get('llm', {})
        self.llm = LLMConfig(
            model_name=os.getenv('LLM_MODEL', llm_config.get('model_name', 'gpt-3.5-turbo')),
            api_key=os.getenv('OPENAI_API_KEY', llm_config.get('api_key', '')),
            max_tokens=int(os.getenv('LLM_MAX_TOKENS', llm_config.get('max_tokens', 1000)))
        )

        # UI configuration
        ui_config = config_data.get('ui', {})
        self.ui = UIConfig(
            streamlit_port=int(os.getenv('STREAMLIT_PORT', ui_config.get('streamlit_port', 8501))),
            max_file_size_mb=int(os.getenv('MAX_FILE_SIZE_MB', ui_config.get('max_file_size_mb', 200)))
        )

        # Paths
        self.base_dir = Path(__file__).parent
        self.data_dir = self.base_dir / "data"
        self.models_dir = self.base_dir / "models"
        self.logs_dir = self.base_dir / "logs"

        # Create directories if they don't exist
        for dir_path in [self.data_dir, self.models_dir, self.logs_dir]:
            dir_path.mkdir(exist_ok=True)

# Global configuration instance
config = Config()

# Logging configuration
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s'
        }
    },
    'handlers': {
        'default': {
            'level': 'INFO',
            'formatter': 'standard',
            'class': 'logging.StreamHandler',
        },
        'file': {
            'level': 'DEBUG',
            'formatter': 'detailed',
            'class': 'logging.FileHandler',
            'filename': str(config.logs_dir / 'app.log'),
            'mode': 'a',
        },
    },
    'loggers': {
        '': {
            'handlers': ['default', 'file'],
            'level': 'DEBUG',
            'propagate': False
        }
    }
}