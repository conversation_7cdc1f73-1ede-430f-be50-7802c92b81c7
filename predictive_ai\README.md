# AI Data Science Assistant

An interactive AI-powered data science pipeline that guides users through the complete machine learning workflow: dataset discovery → cleaning → problem detection → multi-model training → evaluation → final model saving.

## Features

- **Interactive Pipeline**: Step-by-step guidance through the data science workflow
- **Smart Dataset Discovery**: Automatically finds and suggests relevant datasets
- **Automated Data Cleaning**: Intelligent preprocessing with user approval/rejection
- **Problem Type Detection**: Automatically determines if it's classification, regression, or clustering
- **Multi-Model Training**: Trains multiple algorithms and compares performance
- **Context-Aware AI Chat**: LLM assistant that understands your current pipeline step
- **Async Task Queue**: Celery-based queue prevents system overload (concurrency=3)
- **Web Interface**: Beautiful Streamlit UI with real-time updates

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit UI  │    │  Client Agent   │    │   MCP Server    │
│                 │◄──►│                 │◄──►│                 │
│ - File Upload   │    │ - Orchestrator  │    │ - FastAPI       │
│ - Chat Interface│    │ - LLM Chat      │    │ - Tool Endpoints│
│ - Progress View │    │ - Feedback      │    │ - Task Queue    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                │                       ▼
                                │               ┌─────────────────┐
                                │               │  Celery Workers │
                                │               │                 │
                                │               │ - Dataset Tools │
                                │               │ - M<PERSON> Tools      │
                                │               │ - Cleaning Tools│
                                │               └─────────────────┘
                                │                       │
                                ▼                       ▼
                        ┌─────────────────┐    ┌─────────────────┐
                        │   PostgreSQL    │    │     Redis       │
                        │                 │    │                 │
                        │ - Datasets      │    │ - Task Queue    │
                        │ - Metadata      │    │ - Results Cache │
                        └─────────────────┘    └─────────────────┘
```

## Installation

### Prerequisites

1. **Python 3.8+**
2. **PostgreSQL** (for dataset storage)
3. **Redis** (for task queue)

### Setup

1. **Clone and navigate to the project:**
```bash
cd predictive_ai
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Configure environment variables:**
Edit `.env` file with your settings:
```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=predictive_ai
DB_USER=postgres
DB_PASSWORD=your_password_here

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here
```

4. **Setup PostgreSQL database:**
```sql
CREATE DATABASE predictive_ai;
-- Add your datasets as tables in this database
```

5. **Start Redis server:**
```bash
redis-server
```

## Running the Application

### Option 1: Start All Components (Recommended for Development)

```bash
python -m predictive_ai.main all
```

This starts:
- Celery worker (background)
- FastAPI server (background)
- Streamlit UI (foreground)

### Option 2: Start Components Separately

**Terminal 1 - Start Celery Worker:**
```bash
python -m predictive_ai.main worker
```

**Terminal 2 - Start FastAPI Server:**
```bash
python -m predictive_ai.main server
```

**Terminal 3 - Start Streamlit UI:**
```bash
python -m predictive_ai.main ui
```

### Option 3: Using Individual Commands

**Celery Worker:**
```bash
celery -A predictive_ai.mcp_server.celery worker --loglevel=info --concurrency=3
```

**FastAPI Server:**
```bash
uvicorn predictive_ai.mcp_server.server:app --host 0.0.0.0 --port 8000
```

**Streamlit UI:**
```bash
streamlit run predictive_ai/ui/streamlit_ui.py --server.port 8501
```

## Usage

### 1. Access the Web Interface
Open your browser and go to: `http://localhost:8501`

### 2. Upload Dataset or Connect to Database
- **File Upload**: Use the sidebar to upload CSV, Excel, or JSON files
- **Database**: Click "Connect to PostgreSQL" to access database tables

### 3. Start Analysis Pipeline
1. Describe your analysis goal (e.g., "I want to predict house prices")
2. Click "🚀 Start Pipeline"
3. The system will guide you through each step:
   - **Dataset Discovery**: AI suggests relevant datasets
   - **Data Cleaning**: Automatic preprocessing with approval options
   - **Problem Detection**: Determines ML problem type
   - **Model Training**: Trains multiple algorithms
   - **Model Evaluation**: Compares and selects best model
   - **Hyperparameter Tuning**: Optimizes final model

### 4. Interact with AI Assistant
- Use the chat interface to ask questions about any step
- Get explanations of recommendations
- Request modifications to the pipeline

### 5. Approve/Reject/Modify Steps
At each step, you can:
- ✅ **Approve**: Accept recommendations and proceed
- ❌ **Reject**: Get fallback options or manual controls
- ⚙️ **Modify**: Adjust parameters and re-run

## API Endpoints

The FastAPI server provides REST endpoints:

- `GET /` - Health check
- `POST /pipeline/start` - Start new pipeline
- `GET /pipeline/{session_id}/status` - Get pipeline status
- `POST /pipeline/execute` - Execute pipeline step
- `GET /task/{task_id}/result` - Get task result
- `POST /pipeline/feedback` - Handle user feedback
- `POST /chat` - Chat with AI assistant
- `GET /datasets/suggest` - Get dataset suggestions
- `GET /datasets/list` - List all datasets

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | PostgreSQL host | localhost |
| `DB_PORT` | PostgreSQL port | 5432 |
| `DB_NAME` | Database name | predictive_ai |
| `DB_USER` | Database user | postgres |
| `DB_PASSWORD` | Database password | - |
| `CELERY_BROKER_URL` | Redis URL for Celery | redis://localhost:6379/0 |
| `OPENAI_API_KEY` | OpenAI API key | - |
| `MAX_MODELS` | Max models per problem | 5 |
| `STREAMLIT_PORT` | Streamlit port | 8501 |

### File Structure

```
predictive_ai/
├── config.py              # Configuration management
├── main.py                # Application entry point
├── requirements.txt       # Python dependencies
├── .env                   # Environment variables
├── client_agent/          # Orchestration layer
│   ├── orchestrator.py    # Pipeline flow control
│   └── llm_chat_agent.py  # AI chat assistant
├── mcp_server/            # Backend engine
│   ├── server.py          # FastAPI server
│   ├── celery.py          # Task queue setup
│   ├── models.py          # Data models
│   └── tools/             # Pipeline tools
│       ├── suggest_datasets.py
│       ├── list_available_datasets.py
│       ├── clean_data.py
│       ├── detect_problem_type.py
│       ├── train_model.py
│       ├── train_multiple_models.py
│       ├── evaluate_model.py
│       ├── hyperparam_tune.py
│       ├── get_insights.py
│       └── fallback_recommender.py
└── ui/                    # Web interface
    └── streamlit_ui.py    # Streamlit application
```

## Troubleshooting

### Common Issues

1. **Redis Connection Error**
   - Ensure Redis server is running: `redis-server`
   - Check Redis URL in `.env`

2. **PostgreSQL Connection Error**
   - Verify database credentials in `.env`
   - Ensure PostgreSQL is running and database exists

3. **OpenAI API Error**
   - Check API key in `.env`
   - Verify API quota and billing

4. **Import Errors**
   - Install all dependencies: `pip install -r requirements.txt`
   - Check Python version (3.8+ required)

### Logs

Application logs are stored in `logs/app.log`. Check this file for detailed error information.

### Performance

- **Celery Concurrency**: Limited to 3 workers to prevent system overload
- **File Size Limit**: 200MB max upload size
- **Task Timeout**: 30 minutes per ML task

## Development

### Adding New Tools

1. Create tool file in `mcp_server/tools/`
2. Implement Celery task with `@celery_app.task(bind=True)`
3. Add tool to orchestrator execution logic
4. Update UI to handle new step

### Testing

```bash
pytest tests/
```

### Code Quality

```bash
black predictive_ai/
flake8 predictive_ai/
```

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review logs in `logs/app.log`
3. Open an issue on the project repository