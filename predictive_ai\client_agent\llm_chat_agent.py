"""
LLM Chat Agent - Context-aware AI assistant for the pipeline
"""
import logging
from typing import Dict, Any, List, Optional
import openai
from datetime import datetime

from ..config import config
from ..mcp_server.models import PipelineStep, pipeline_state

logger = logging.getLogger(__name__)

class LLMChatAgent:
    """Context-aware LLM chat agent for the data science pipeline"""

    def __init__(self):
        self.config = config.llm
        openai.api_key = self.config.api_key

    def get_response(self, user_message: str, context: Dict[str, Any]) -> str:
        """Get AI response with pipeline context"""
        try:
            # Build context-aware system prompt
            system_prompt = self._build_system_prompt(context)

            # Create messages for the conversation
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]

            # Get response from OpenAI
            response = openai.ChatCompletion.create(
                model=self.config.model_name,
                messages=messages,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Error getting LLM response: {e}")
            return f"I apologize, but I encountered an error: {e}. Please try again."

    def _build_system_prompt(self, context: Dict[str, Any]) -> str:
        """Build context-aware system prompt"""
        base_prompt = self.config.system_prompt

        # Add current pipeline context
        context_info = []

        if context.get('session_id'):
            context_info.append(f"Current session: {context['session_id']}")

        if context.get('current_step'):
            step_descriptions = {
                'dataset_discovery': 'finding and suggesting the best datasets for analysis',
                'data_cleaning': 'cleaning and preprocessing the selected dataset',
                'problem_detection': 'analyzing data to determine the type of ML problem',
                'model_training': 'training multiple machine learning models',
                'model_evaluation': 'evaluating and comparing model performance',
                'hyperparameter_tuning': 'optimizing the best model parameters'
            }

            current_step = context['current_step']
            step_desc = step_descriptions.get(current_step, current_step)
            context_info.append(f"Current pipeline step: {step_desc}")

        # Add step results context
        if context.get('step_results'):
            context_info.append("Previous step results available for reference")

            # Add specific context for each completed step
            for step, result in context['step_results'].items():
                if step == 'dataset_discovery':
                    datasets = result.get('suggestions', [])
                    context_info.append(f"Found {len(datasets)} dataset suggestions")

                elif step == 'data_cleaning':
                    summary = result.get('summary', '')
                    context_info.append(f"Data cleaning completed: {summary}")

                elif step == 'problem_detection':
                    problem_type = result.get('problem_type', 'unknown')
                    confidence = result.get('confidence', 0)
                    context_info.append(f"Detected problem type: {problem_type} (confidence: {confidence:.1%})")

                elif step == 'model_training':
                    models = result.get('trained_models', [])
                    context_info.append(f"Trained {len(models)} models")

                elif step == 'model_evaluation':
                    best_model = result.get('best_model', {})
                    if best_model:
                        model_name = best_model.get('model_name', 'unknown')
                        context_info.append(f"Best model identified: {model_name}")

        # Combine base prompt with context
        if context_info:
            context_str = "\n".join([f"- {info}" for info in context_info])
            enhanced_prompt = f"""{base_prompt}

Current Pipeline Context:
{context_str}

Please provide helpful, context-aware responses about the current step, previous results, and next steps in the data science pipeline. You can explain what happened in previous steps, what's currently happening, and what to expect next."""
        else:
            enhanced_prompt = base_prompt

        return enhanced_prompt

    def explain_step(self, step: PipelineStep, result: Dict[str, Any] = None) -> str:
        """Explain a specific pipeline step and its results"""
        explanations = {
            PipelineStep.DATASET_DISCOVERY: self._explain_dataset_discovery,
            PipelineStep.DATA_CLEANING: self._explain_data_cleaning,
            PipelineStep.PROBLEM_DETECTION: self._explain_problem_detection,
            PipelineStep.MODEL_TRAINING: self._explain_model_training,
            PipelineStep.MODEL_EVALUATION: self._explain_model_evaluation,
            PipelineStep.HYPERPARAMETER_TUNING: self._explain_hyperparameter_tuning
        }

        explanation_func = explanations.get(step)
        if explanation_func:
            return explanation_func(result)
        else:
            return f"Step {step.value} is part of the data science pipeline."

    def _explain_dataset_discovery(self, result: Dict[str, Any] = None) -> str:
        """Explain dataset discovery step"""
        base_explanation = """Dataset Discovery analyzes your request and finds the most relevant datasets from available sources (files and databases). It scores datasets based on:
- Column name relevance to your request
- Dataset size and quality
- Domain-specific keywords"""

        if result:
            suggestions = result.get('suggestions', [])
            if suggestions:
                base_explanation += f"\n\nFound {len(suggestions)} dataset suggestions. The top suggestion is '{suggestions[0]['name']}' with {suggestions[0]['rows']} rows and {suggestions[0]['columns']} columns."

        return base_explanation

    def _explain_data_cleaning(self, result: Dict[str, Any] = None) -> str:
        """Explain data cleaning step"""
        base_explanation = """Data Cleaning automatically preprocesses your dataset by:
- Removing columns with >50% missing values
- Filling missing values (median for numeric, mode for categorical)
- Removing duplicate rows
- Detecting and removing outliers using IQR method
- Dropping constant columns"""

        if result:
            summary = result.get('summary', '')
            if summary:
                base_explanation += f"\n\nCleaning Results: {summary}"

        return base_explanation

    def _explain_problem_detection(self, result: Dict[str, Any] = None) -> str:
        """Explain problem detection step"""
        base_explanation = """Problem Detection analyzes your target column to determine the type of machine learning problem:
- Regression: For continuous numeric targets
- Classification: For categorical targets or discrete numeric values
- Clustering: When no clear target is identified"""

        if result:
            problem_type = result.get('problem_type', '')
            confidence = result.get('confidence', 0)
            reason = result.get('reason', '')

            if problem_type:
                base_explanation += f"\n\nDetected: {problem_type.title()} (confidence: {confidence:.1%})\nReason: {reason}"

        return base_explanation

    def _explain_model_training(self, result: Dict[str, Any] = None) -> str:
        """Explain model training step"""
        base_explanation = """Model Training trains multiple algorithms suitable for your problem type:
- For Classification: Random Forest, Logistic Regression, XGBoost, SVM, Gradient Boosting
- For Regression: Random Forest, Linear Regression, XGBoost, SVM, Gradient Boosting
- Uses cross-validation for robust performance estimation"""

        if result:
            models = result.get('trained_models', [])
            if models:
                base_explanation += f"\n\nTrained {len(models)} models. Performance varies based on your data characteristics."

        return base_explanation

    def _explain_model_evaluation(self, result: Dict[str, Any] = None) -> str:
        """Explain model evaluation step"""
        base_explanation = """Model Evaluation compares all trained models using appropriate metrics:
- Classification: Accuracy, Precision, Recall, F1-score
- Regression: MAE, MSE, RMSE, R²
- Selects the best performing model for your specific dataset"""

        if result:
            best_model = result.get('best_model', {})
            if best_model:
                model_name = best_model.get('model_name', 'unknown')
                base_explanation += f"\n\nBest Model: {model_name}"

        return base_explanation

    def _explain_hyperparameter_tuning(self, result: Dict[str, Any] = None) -> str:
        """Explain hyperparameter tuning step"""
        base_explanation = """Hyperparameter Tuning optimizes the best model's parameters using Optuna:
- Searches for optimal parameter combinations
- Uses cross-validation to avoid overfitting
- Typically improves model performance by 2-10%"""

        if result:
            improvement = result.get('improvement', 0)
            if improvement:
                base_explanation += f"\n\nAchieved {improvement:.2%} performance improvement through optimization."

        return base_explanation

# Global chat agent instance
chat_agent = LLMChatAgent()