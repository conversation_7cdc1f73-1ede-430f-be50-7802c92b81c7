"""
Problem type detection tool - analyzes data to recommend problem type
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple
from ..celery import celery_app
from ..models import ProblemType, DatasetInfo
from ...config import config

logger = logging.getLogger(__name__)

class ProblemTypeDetector:
    """Detects the type of ML problem based on data characteristics"""

    def analyze_target_column(self, df: pd.DataFrame, target_col: str) -> Dict[str, Any]:
        """Analyze target column to determine problem type"""
        if target_col not in df.columns:
            raise ValueError(f"Target column '{target_col}' not found in dataset")

        target_series = df[target_col].dropna()

        analysis = {
            'column_name': target_col,
            'data_type': str(target_series.dtype),
            'unique_values': target_series.nunique(),
            'total_values': len(target_series),
            'missing_values': df[target_col].isnull().sum(),
            'sample_values': target_series.head(10).tolist()
        }

        # Determine if numeric or categorical
        if pd.api.types.is_numeric_dtype(target_series):
            analysis['is_numeric'] = True
            analysis['min_value'] = target_series.min()
            analysis['max_value'] = target_series.max()
            analysis['mean_value'] = target_series.mean()
            analysis['std_value'] = target_series.std()
        else:
            analysis['is_numeric'] = False
            analysis['value_counts'] = target_series.value_counts().head(10).to_dict()

        return analysis

    def detect_problem_type(self, df: pd.DataFrame, target_col: str = None) -> Dict[str, Any]:
        """Detect the most likely problem type"""
        logger.info(f"Detecting problem type for dataset with shape: {df.shape}")

        if target_col is None:
            # Try to auto-detect target column
            target_col = self._auto_detect_target(df)

        if target_col is None:
            return {
                'problem_type': ProblemType.CLUSTERING.value,
                'confidence': 0.8,
                'reason': 'No clear target column found, suggesting unsupervised learning',
                'recommendations': ['Consider clustering analysis', 'Perform exploratory data analysis']
            }

        target_analysis = self.analyze_target_column(df, target_col)

        # Decision logic for problem type
        if target_analysis['is_numeric']:
            unique_ratio = target_analysis['unique_values'] / target_analysis['total_values']

            if unique_ratio > 0.05:  # Continuous values
                return {
                    'problem_type': ProblemType.REGRESSION.value,
                    'confidence': 0.9,
                    'reason': f'Target column is numeric with high cardinality ({target_analysis["unique_values"]} unique values)',
                    'target_column': target_col,
                    'target_analysis': target_analysis,
                    'recommendations': ['Use regression algorithms', 'Check for outliers', 'Consider feature scaling']
                }
            else:  # Discrete numeric values
                if target_analysis['unique_values'] <= 10:
                    return {
                        'problem_type': ProblemType.CLASSIFICATION.value,
                        'confidence': 0.8,
                        'reason': f'Target column is numeric but with low cardinality ({target_analysis["unique_values"]} classes)',
                        'target_column': target_col,
                        'target_analysis': target_analysis,
                        'recommendations': ['Use classification algorithms', 'Check class balance', 'Consider encoding if needed']
                    }
        else:
            # Categorical target
            unique_count = target_analysis['unique_values']

            if unique_count <= 20:  # Reasonable number of classes
                return {
                    'problem_type': ProblemType.CLASSIFICATION.value,
                    'confidence': 0.9,
                    'reason': f'Target column is categorical with {unique_count} classes',
                    'target_column': target_col,
                    'target_analysis': target_analysis,
                    'recommendations': ['Use classification algorithms', 'Check class balance', 'Consider one-hot encoding for features']
                }
            else:
                return {
                    'problem_type': ProblemType.CLUSTERING.value,
                    'confidence': 0.6,
                    'reason': f'Target column has too many categories ({unique_count}), consider unsupervised learning',
                    'target_column': target_col,
                    'target_analysis': target_analysis,
                    'recommendations': ['Consider clustering', 'Reduce dimensionality', 'Group similar categories']
                }

        # Default fallback
        return {
            'problem_type': ProblemType.CLASSIFICATION.value,
            'confidence': 0.5,
            'reason': 'Unable to determine clear problem type',
            'target_column': target_col,
            'target_analysis': target_analysis,
            'recommendations': ['Manual review needed', 'Consider domain expertise']
        }

    def _auto_detect_target(self, df: pd.DataFrame) -> str:
        """Try to automatically detect the target column"""
        # Common target column names
        target_keywords = ['target', 'label', 'class', 'y', 'output', 'result', 'price', 'value', 'score']

        for col in df.columns:
            col_lower = col.lower()
            for keyword in target_keywords:
                if keyword in col_lower:
                    return col

        # If no keyword match, return the last column (common convention)
        return df.columns[-1] if len(df.columns) > 1 else None

@celery_app.task(bind=True)
def detect_problem_type_task(self, dataset_path: str, target_column: str = None):
    """Celery task to detect problem type"""
    try:
        logger.info(f"Starting problem type detection for: {dataset_path}")

        # Load dataset
        df = pd.read_csv(dataset_path)

        detector = ProblemTypeDetector()
        result = detector.detect_problem_type(df, target_column)

        logger.info(f"Problem type detection completed: {result['problem_type']}")
        return result

    except Exception as e:
        logger.error(f"Problem type detection failed: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)