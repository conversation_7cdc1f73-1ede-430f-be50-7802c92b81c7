"""
Streamlit UI for the AI Data Science Pipeline
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, Any, List
import time
import json
from pathlib import Path

# Import our modules
from ..client_agent.orchestrator import orchestrator
from ..client_agent.llm_chat_agent import chat_agent
from ..mcp_server.models import PipelineStep, DatasetInfo
from ..config import config

# Page configuration
st.set_page_config(
    page_title="AI Data Science Assistant",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .step-container {
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
    }
    .step-approved {
        border-color: #4CAF50;
        background-color: #f1f8e9;
    }
    .step-pending {
        border-color: #ff9800;
        background-color: #fff3e0;
    }
    .step-rejected {
        border-color: #f44336;
        background-color: #ffebee;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize Streamlit session state"""
    if 'session_id' not in st.session_state:
        st.session_state.session_id = None
    if 'pipeline_started' not in st.session_state:
        st.session_state.pipeline_started = False
    if 'current_step' not in st.session_state:
        st.session_state.current_step = None
    if 'step_results' not in st.session_state:
        st.session_state.step_results = {}
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []

def main():
    """Main Streamlit application"""
    initialize_session_state()

    # Header
    st.markdown('<h1 class="main-header">🤖 AI Data Science Assistant</h1>', unsafe_allow_html=True)

    # Sidebar
    with st.sidebar:
        st.header("Pipeline Control")

        # File upload section
        st.subheader("📁 Upload Dataset")
        uploaded_file = st.file_uploader(
            "Choose a file",
            type=['csv', 'xlsx', 'xls', 'json'],
            help="Upload your dataset file"
        )

        if uploaded_file is not None:
            # Save uploaded file
            save_path = config.data_dir / uploaded_file.name
            with open(save_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            st.success(f"File uploaded: {uploaded_file.name}")

        # Database connection section
        st.subheader("🗄️ Database Connection")
        if st.button("Connect to PostgreSQL"):
            try:
                # Test database connection
                st.success("Connected to database!")
            except Exception as e:
                st.error(f"Connection failed: {e}")

        # Pipeline status
        if st.session_state.pipeline_started:
            st.subheader("📊 Pipeline Status")
            status = orchestrator.get_pipeline_status(st.session_state.session_id)
            st.json(status)