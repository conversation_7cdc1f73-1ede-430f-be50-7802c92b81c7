"""
Dataset suggestion tool - finds best matching datasets based on user request
"""
import logging
import os
import pandas as pd
import sqlalchemy as sa
from typing import List, Dict, Any, Optional
from pathlib import Path
import re
from datetime import datetime

from ..celery import celery_app
from ..models import DatasetInfo, TaskStatus
from ...config import config

logger = logging.getLogger(__name__)

class DatasetSuggester:
    """Suggests datasets based on user requirements"""

    def __init__(self):
        self.data_dir = config.data_dir
        self.db_config = config.database

    def _get_database_datasets(self) -> List[DatasetInfo]:
        """Get datasets from PostgreSQL database"""
        datasets = []
        try:
            engine = sa.create_engine(self.db_config.connection_string)

            # Query to get all tables
            query = """
            SELECT
                table_name,
                pg_size_pretty(pg_total_relation_size(quote_ident(table_name))) as size,
                (SELECT COUNT(*) FROM information_schema.columns
                 WHERE table_name = t.table_name) as column_count
            FROM information_schema.tables t
            WHERE table_schema = 'public'
            AND table_type = 'BASE TABLE'
            """

            with engine.connect() as conn:
                result = conn.execute(sa.text(query))
                for row in result:
                    table_name = row[0]

                    # Get row count
                    count_query = f"SELECT COUNT(*) FROM {table_name}"
                    row_count = conn.execute(sa.text(count_query)).scalar()

                    # Get column info
                    col_query = f"""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns
                    WHERE table_name = '{table_name}'
                    ORDER BY ordinal_position
                    """
                    col_result = conn.execute(sa.text(col_query))
                    columns = {row[0]: {'type': row[1], 'nullable': row[2]}
                              for row in col_result}

                    dataset = DatasetInfo(
                        name=table_name,
                        path=f"database://{table_name}",
                        source="database",
                        size_mb=0.0,  # Would need more complex query for actual size
                        rows=row_count,
                        columns=len(columns),
                        description=f"Database table: {table_name}",
                        column_info=columns,
                        data_types={col: info['type'] for col, info in columns.items()}
                    )
                    datasets.append(dataset)

        except Exception as e:
            logger.error(f"Error accessing database: {e}")

        return datasets

    def _get_file_datasets(self) -> List[DatasetInfo]:
        """Get datasets from file system"""
        datasets = []

        if not self.data_dir.exists():
            logger.warning(f"Data directory {self.data_dir} does not exist")
            return datasets

        # Supported file extensions
        extensions = ['.csv', '.xlsx', '.xls', '.json', '.txt']

        for ext in extensions:
            for file_path in self.data_dir.glob(f"**/*{ext}"):
                try:
                    # Get file info
                    file_size = file_path.stat().st_size / (1024 * 1024)  # MB

                    # Try to read file to get shape and column info
                    df = None
                    if ext == '.csv':
                        df = pd.read_csv(file_path, nrows=1000)  # Sample for analysis
                    elif ext in ['.xlsx', '.xls']:
                        df = pd.read_excel(file_path, nrows=1000)
                    elif ext == '.json':
                        df = pd.read_json(file_path, nrows=1000)

                    if df is not None:
                        # Get missing values info
                        missing_values = df.isnull().sum().to_dict()
                        data_types = df.dtypes.astype(str).to_dict()

                        # Get full row count for CSV files
                        if ext == '.csv':
                            try:
                                full_df = pd.read_csv(file_path)
                                total_rows = len(full_df)
                            except:
                                total_rows = len(df)
                        else:
                            total_rows = len(df)

                        dataset = DatasetInfo(
                            name=file_path.stem,
                            path=str(file_path),
                            source="file",
                            size_mb=file_size,
                            rows=total_rows,
                            columns=len(df.columns),
                            description=f"File dataset: {file_path.name}",
                            column_info={col: {'type': str(dtype)} for col, dtype in data_types.items()},
                            missing_values=missing_values,
                            data_types=data_types
                        )
                        datasets.append(dataset)

                except Exception as e:
                    logger.warning(f"Error processing file {file_path}: {e}")

        return datasets

    def _score_dataset_relevance(self, dataset: DatasetInfo, user_request: str) -> float:
        """Score how relevant a dataset is to the user request"""
        score = 0.0
        request_lower = user_request.lower()

        # Keywords that might indicate the domain/type of analysis
        keywords = {
            'house': ['price', 'real estate', 'property', 'housing'],
            'sales': ['revenue', 'profit', 'customer', 'product'],
            'stock': ['price', 'market', 'trading', 'financial'],
            'weather': ['temperature', 'climate', 'precipitation'],
            'health': ['medical', 'patient', 'diagnosis', 'treatment'],
            'education': ['student', 'grade', 'school', 'university'],
            'marketing': ['campaign', 'advertisement', 'conversion'],
            'fraud': ['transaction', 'payment', 'suspicious'],
            'sentiment': ['review', 'opinion', 'text', 'comment']
        }

        # Check dataset name relevance
        dataset_name_lower = dataset.name.lower()
        for domain, domain_keywords in keywords.items():
            if domain in request_lower:
                if domain in dataset_name_lower:
                    score += 50
                for keyword in domain_keywords:
                    if keyword in dataset_name_lower:
                        score += 20

        # Check column names relevance
        if dataset.column_info:
            for col_name in dataset.column_info.keys():
                col_lower = col_name.lower()
                for word in request_lower.split():
                    if len(word) > 3 and word in col_lower:
                        score += 10

        # Prefer datasets with reasonable size (not too small, not too large)
        if 100 <= dataset.rows <= 100000:
            score += 10
        elif dataset.rows > 100000:
            score += 5

        # Prefer datasets with multiple columns
        if dataset.columns >= 5:
            score += 5

        return score

    def suggest_datasets(self, user_request: str, max_suggestions: int = 5) -> List[DatasetInfo]:
        """Suggest best matching datasets"""
        logger.info(f"Suggesting datasets for request: {user_request}")

        # Get all available datasets
        all_datasets = []
        all_datasets.extend(self._get_file_datasets())
        all_datasets.extend(self._get_database_datasets())

        if not all_datasets:
            logger.warning("No datasets found")
            return []

        # Score and rank datasets
        scored_datasets = []
        for dataset in all_datasets:
            score = self._score_dataset_relevance(dataset, user_request)
            scored_datasets.append((score, dataset))

        # Sort by score (descending) and return top suggestions
        scored_datasets.sort(key=lambda x: x[0], reverse=True)
        suggestions = [dataset for score, dataset in scored_datasets[:max_suggestions]]

        logger.info(f"Found {len(suggestions)} dataset suggestions")
        return suggestions

# Celery task
@celery_app.task(bind=True)
def suggest_datasets_task(self, user_request: str, max_suggestions: int = 5):
    """Celery task to suggest datasets"""
    try:
        logger.info(f"Starting dataset suggestion task for: {user_request}")

        suggester = DatasetSuggester()
        suggestions = suggester.suggest_datasets(user_request, max_suggestions)

        result = {
            'suggestions': [dataset.to_dict() for dataset in suggestions],
            'total_found': len(suggestions),
            'user_request': user_request
        }

        logger.info(f"Dataset suggestion task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Dataset suggestion task failed: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)