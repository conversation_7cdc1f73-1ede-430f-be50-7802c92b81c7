"""
Main entry point for the AI Data Science Pipeline
"""
import logging
import logging.config
import argparse
import sys
from pathlib import Path

# Setup logging
from .config import LOGGING_CONFIG
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger(__name__)

def start_celery_worker():
    """Start Celery worker"""
    logger.info("Starting Celery worker...")
    from .mcp_server.celery import celery_app

    # Start worker with concurrency=3
    celery_app.worker_main([
        'worker',
        '--loglevel=info',
        '--concurrency=3',
        '--queues=ml_tasks'
    ])

def start_streamlit_ui():
    """Start Streamlit UI"""
    logger.info("Starting Streamlit UI...")
    import subprocess
    import sys

    # Get the path to the UI module
    ui_path = Path(__file__).parent / "ui" / "streamlit_ui.py"

    # Start Streamlit
    subprocess.run([
        sys.executable, "-m", "streamlit", "run",
        str(ui_path),
        "--server.port=8501",
        "--server.address=0.0.0.0"
    ])

def start_mcp_server():
    """Start MCP server"""
    logger.info("Starting MCP server...")
    from .mcp_server.server import run_mcp_server
    import asyncio

    asyncio.run(run_mcp_server())

def setup_database():
    """Setup database tables if needed"""
    logger.info("Setting up database...")
    try:
        from .config import config
        import sqlalchemy as sa

        # Test database connection
        engine = sa.create_engine(config.database.connection_string)
        with engine.connect() as conn:
            # Test connection
            conn.execute(sa.text("SELECT 1"))

        logger.info("Database connection successful")
        return True

    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        return False

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="AI Data Science Pipeline")
    parser.add_argument(
        "component",
        choices=["worker", "ui", "server", "all"],
        help="Component to start"
    )
    parser.add_argument(
        "--setup-db",
        action="store_true",
        help="Setup database before starting"
    )

    args = parser.parse_args()

    # Setup database if requested
    if args.setup_db:
        if not setup_database():
            sys.exit(1)

    # Start the requested component
    if args.component == "worker":
        start_celery_worker()
    elif args.component == "ui":
        start_streamlit_ui()
    elif args.component == "server":
        start_mcp_server()
    elif args.component == "all":
        # Start all components (for development)
        import multiprocessing
        import time

        # Start worker in background
        worker_process = multiprocessing.Process(target=start_celery_worker)
        worker_process.start()

        # Start server in background
        server_process = multiprocessing.Process(target=start_mcp_server)
        server_process.start()

        # Give services time to start
        time.sleep(3)

        # Start UI in foreground
        try:
            start_streamlit_ui()
        except KeyboardInterrupt:
            logger.info("Shutting down...")
            worker_process.terminate()
            server_process.terminate()
            worker_process.join()
            server_process.join()

if __name__ == "__main__":
    main()