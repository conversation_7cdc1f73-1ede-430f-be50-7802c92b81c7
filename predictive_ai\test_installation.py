#!/usr/bin/env python3
"""
Test script to verify the AI Data Science Pipeline installation
"""
import sys
import importlib
from pathlib import Path

def test_import(module_name, package_name=None):
    """Test if a module can be imported"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {package_name or module_name}")
        return True
    except ImportError as e:
        print(f"❌ {package_name or module_name}: {e}")
        return False

def test_service(service_name, host, port):
    """Test if a service is running"""
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ {service_name} is running on {host}:{port}")
            return True
        else:
            print(f"❌ {service_name} is not running on {host}:{port}")
            return False
    except Exception as e:
        print(f"❌ {service_name}: {e}")
        return False

def main():
    """Run installation tests"""
    print("🧪 Testing AI Data Science Pipeline Installation")
    print("=" * 50)
    
    # Test Python version
    print(f"Python version: {sys.version}")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    else:
        print("✅ Python version OK")
    
    print("\n📦 Testing Core Dependencies:")
    core_deps = [
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("sklearn", "scikit-learn"),
        ("fastapi", "fastapi"),
        ("streamlit", "streamlit"),
        ("celery", "celery"),
        ("redis", "redis"),
        ("pydantic", "pydantic"),
        ("yaml", "pyyaml"),
        ("dotenv", "python-dotenv")
    ]
    
    core_success = 0
    for module, package in core_deps:
        if test_import(module, package):
            core_success += 1
    
    print(f"\nCore dependencies: {core_success}/{len(core_deps)} installed")
    
    # Test ML dependencies
    print("\n🤖 Testing ML Dependencies:")
    ml_deps = [
        ("xgboost", "xgboost"),
        ("lightgbm", "lightgbm"),
        ("matplotlib", "matplotlib"),
        ("seaborn", "seaborn"),
        ("plotly", "plotly"),
        ("optuna", "optuna")
    ]
    
    ml_success = 0
    for module, package in ml_deps:
        if test_import(module, package):
            ml_success += 1
    
    print(f"\nML dependencies: {ml_success}/{len(ml_deps)} installed")
    
    # Test database dependencies
    print("\n🗄️ Testing Database Dependencies:")
    db_deps = [
        ("sqlalchemy", "sqlalchemy"),
        ("psycopg2", "psycopg2-binary")
    ]
    
    db_success = 0
    for module, package in db_deps:
        if test_import(module, package):
            db_success += 1
    
    print(f"\nDatabase dependencies: {db_success}/{len(db_deps)} installed")
    
    # Test LLM dependencies
    print("\n🧠 Testing LLM Dependencies:")
    llm_deps = [
        ("openai", "openai"),
        ("langchain", "langchain")
    ]
    
    llm_success = 0
    for module, package in llm_deps:
        if test_import(module, package):
            llm_success += 1
    
    print(f"\nLLM dependencies: {llm_success}/{len(llm_deps)} installed")
    
    # Test MCP dependencies
    print("\n🔗 Testing MCP Dependencies:")
    mcp_deps = [
        ("fastmcp", "fastmcp"),
        ("mcp", "mcp")
    ]
    
    mcp_success = 0
    for module, package in mcp_deps:
        if test_import(module, package):
            mcp_success += 1
    
    print(f"\nMCP dependencies: {mcp_success}/{len(mcp_deps)} installed")
    if mcp_success == 0:
        print("ℹ️  MCP not available - will use FastAPI fallback")
    
    # Test services
    print("\n🔧 Testing Services:")
    print("Testing Redis...")
    redis_ok = test_service("Redis", "localhost", 6379)
    
    print("Testing PostgreSQL...")
    postgres_ok = test_service("PostgreSQL", "localhost", 5432)
    
    # Test project structure
    print("\n📁 Testing Project Structure:")
    required_files = [
        "config.py",
        ".env",
        "requirements.txt",
        "mcp_server/models.py",
        "mcp_server/celery.py",
        "client_agent/orchestrator.py",
        "ui/streamlit_ui.py",
        "data/sample_house_prices.csv"
    ]
    
    structure_ok = 0
    for file_path in required_files:
        full_path = Path(file_path)
        if full_path.exists():
            print(f"✅ {file_path}")
            structure_ok += 1
        else:
            print(f"❌ {file_path}")
    
    print(f"\nProject structure: {structure_ok}/{len(required_files)} files found")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY:")
    
    total_deps = len(core_deps) + len(ml_deps) + len(db_deps) + len(llm_deps)
    total_success = core_success + ml_success + db_success + llm_success
    
    print(f"Dependencies: {total_success}/{total_deps} installed")
    print(f"Services: Redis {'✅' if redis_ok else '❌'}, PostgreSQL {'✅' if postgres_ok else '❌'}")
    print(f"Project structure: {structure_ok}/{len(required_files)} files")
    
    if total_success >= total_deps * 0.8 and structure_ok >= len(required_files) * 0.8:
        print("\n🎉 Installation looks good! You can try running:")
        print("   python run.py")
        if not redis_ok:
            print("\n⚠️  Start Redis first: redis-server")
        if not postgres_ok:
            print("⚠️  PostgreSQL not detected (optional for file uploads)")
        return True
    else:
        print("\n❌ Installation incomplete. Please install missing dependencies:")
        print("   pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    main()
