"""
FastAPI Server - Fallback implementation when MCP packages are not available
"""
import logging
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import uuid

from .models import PipelineStep, DatasetInfo, pipeline_state
from ..config import config

logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="AI Data Science Pipeline API",
    description="FastAPI Server for the AI Data Science Pipeline (MCP Fallback)",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API
class StartPipelineRequest(BaseModel):
    user_request: str

class ToolCallRequest(BaseModel):
    tool_name: str
    parameters: Dict[str, Any]

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "AI Data Science Pipeline FastAPI Server", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "server_type": "fastapi_fallback"}

@app.post("/tools/call")
async def call_tool(request: ToolCallRequest):
    """Call a tool (MCP-style interface)"""
    try:
        tool_name = request.tool_name
        params = request.parameters
        
        if tool_name == "suggest_datasets":
            from .tools.suggest_datasets import suggest_datasets_task
            task = suggest_datasets_task.delay(
                params.get("user_request"),
                params.get("max_suggestions", 5)
            )
            return {"task_id": task.id, "status": "started"}
            
        elif tool_name == "list_available_datasets":
            from .tools.list_available_datasets import list_available_datasets_task
            task = list_available_datasets_task.delay(params.get("source_filter"))
            return {"task_id": task.id, "status": "started"}
            
        elif tool_name == "clean_data":
            from .tools.clean_data import clean_data_task
            task = clean_data_task.delay(
                params.get("dataset_info"),
                params.get("custom_actions")
            )
            return {"task_id": task.id, "status": "started"}
            
        elif tool_name == "detect_problem_type":
            from .tools.detect_problem_type import detect_problem_type_task
            task = detect_problem_type_task.delay(
                params.get("dataset_path"),
                params.get("target_column")
            )
            return {"task_id": task.id, "status": "started"}
            
        elif tool_name == "train_multiple_models":
            from .tools.train_multiple_models import train_multiple_models_task
            task = train_multiple_models_task.delay(
                params.get("dataset_path"),
                params.get("problem_type"),
                params.get("target_column"),
                params.get("max_models", 5)
            )
            return {"task_id": task.id, "status": "started"}
            
        elif tool_name == "start_pipeline":
            from ..client_agent.orchestrator import orchestrator
            session_id = orchestrator.start_pipeline(params.get("user_request"))
            return {"session_id": session_id, "status": "started"}
            
        elif tool_name == "get_pipeline_status":
            from ..client_agent.orchestrator import orchestrator
            status = orchestrator.get_pipeline_status(params.get("session_id"))
            return status
            
        elif tool_name == "chat_with_assistant":
            from ..client_agent.llm_chat_agent import chat_agent
            
            # Build context
            context = {}
            session_id = params.get("session_id")
            if session_id:
                pipeline_context = pipeline_state.get_context(session_id)
                if pipeline_context:
                    context = {
                        'session_id': session_id,
                        'current_step': pipeline_context.current_step.value,
                        'step_results': {
                            step_result['step']: step_result['result'] 
                            for step_result in pipeline_context.step_history
                        }
                    }
            
            response = chat_agent.get_response(params.get("message"), context)
            return {"response": response, "session_id": session_id}
            
        elif tool_name == "get_task_result":
            from ..client_agent.orchestrator import orchestrator
            result = orchestrator.get_task_result(params.get("task_id"))
            return result
            
        else:
            raise HTTPException(status_code=400, detail=f"Unknown tool: {tool_name}")
            
    except Exception as e:
        logger.error(f"Error calling tool {tool_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/tools/list")
async def list_tools():
    """List available tools"""
    return {
        "tools": [
            "suggest_datasets",
            "list_available_datasets", 
            "clean_data",
            "detect_problem_type",
            "train_multiple_models",
            "start_pipeline",
            "get_pipeline_status",
            "chat_with_assistant",
            "get_task_result"
        ]
    }

# Legacy endpoints for compatibility
@app.post("/pipeline/start")
async def start_pipeline_legacy(request: StartPipelineRequest):
    """Legacy endpoint for starting pipeline"""
    return await call_tool(ToolCallRequest(
        tool_name="start_pipeline",
        parameters={"user_request": request.user_request}
    ))

@app.get("/pipeline/{session_id}/status")
async def get_pipeline_status_legacy(session_id: str):
    """Legacy endpoint for pipeline status"""
    return await call_tool(ToolCallRequest(
        tool_name="get_pipeline_status",
        parameters={"session_id": session_id}
    ))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
