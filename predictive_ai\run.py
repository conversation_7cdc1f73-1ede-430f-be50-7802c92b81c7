#!/usr/bin/env python3
"""
Simple startup script for the AI Data Science Pipeline
"""
import subprocess
import sys
import time
import os
from pathlib import Path

def check_dependencies():
    """Check if required services are running"""
    print("🔍 Checking dependencies...")

    # Check Redis
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis is running")
    except Exception as e:
        print(f"❌ Redis not available: {e}")
        print("   Please start Redis: redis-server")
        return False

    # Check PostgreSQL (optional)
    try:
        import psycopg2
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="postgres",  # Connect to default db first
            user="postgres",
            password=""
        )
        conn.close()
        print("✅ PostgreSQL is available")
    except Exception as e:
        print(f"⚠️  PostgreSQL not available: {e}")
        print("   You can still use file uploads, but database features won't work")

    return True

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")

    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")

    directories = [
        Path("data"),
        Path("models"),
        Path("logs")
    ]

    for directory in directories:
        directory.mkdir(exist_ok=True)
        print(f"   Created: {directory}")

def start_services():
    """Start all services"""
    print("🚀 Starting AI Data Science Pipeline with MCP...")

    # Change to the correct directory
    os.chdir(Path(__file__).parent)

    try:
        # Start Celery worker in background
        print("Starting Celery worker...")
        worker_process = subprocess.Popen([
            sys.executable, "-m", "predictive_ai.main", "worker"
        ])

        time.sleep(2)

        # Start MCP server in background
        print("Starting MCP server...")
        mcp_process = subprocess.Popen([
            sys.executable, "-m", "predictive_ai.main", "server"
        ])

        time.sleep(2)

        # Start Streamlit UI in foreground
        print("Starting Streamlit UI...")
        subprocess.run([
            sys.executable, "-m", "predictive_ai.main", "ui"
        ])

    except KeyboardInterrupt:
        print("\n🛑 Shutting down...")
        try:
            worker_process.terminate()
            mcp_process.terminate()
        except:
            pass
    except Exception as e:
        print(f"❌ Error starting services: {e}")

def main():
    """Main function"""
    print("🤖 AI Data Science Pipeline Startup")
    print("=" * 40)

    # Create directories
    create_directories()

    # Check dependencies
    if not check_dependencies():
        print("\n❌ Please fix dependency issues before continuing")
        return

    # Install Python dependencies
    if not install_dependencies():
        print("\n❌ Please fix installation issues before continuing")
        return

    print("\n🎉 Setup complete! Starting services...")
    print("\nOnce started, you can access:")
    print("  • Web UI: http://localhost:8501")
    print("  • API: http://localhost:8000")
    print("  • API Docs: http://localhost:8000/docs")
    print("\nPress Ctrl+C to stop all services")
    print("=" * 40)

    time.sleep(2)
    start_services()

if __name__ == "__main__":
    main()
