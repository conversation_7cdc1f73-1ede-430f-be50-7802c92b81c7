"""
MCP Server - Model Context Protocol server for AI Data Science Pipeline using FastMCP
"""
import logging
import asyncio
from typing import Dict, Any, List, Optional
import json
import uuid

# FastMCP imports
from fastmcp import FastMCP

from .models import PipelineStep, DatasetInfo, pipeline_state
from .celery import celery_app
from ..config import config

logger = logging.getLogger(__name__)

# Create FastMCP server instance
mcp = FastMCP("AI Data Science Pipeline")

@mcp.tool()
def suggest_datasets(user_request: str, max_suggestions: int = 5) -> Dict[str, Any]:
    """Suggest relevant datasets based on user request"""
    try:
        from .tools.suggest_datasets import suggest_datasets_task

        # Start async task
        task = suggest_datasets_task.delay(user_request, max_suggestions)

        return {
            "task_id": task.id,
            "status": "started",
            "message": f"Finding datasets for: {user_request}"
        }
    except Exception as e:
        logger.error(f"Error in suggest_datasets: {e}")
        return {"error": str(e)}

@mcp.tool()
def list_available_datasets(source_filter: Optional[str] = None) -> Dict[str, Any]:
    """List all available datasets"""
    try:
        from .tools.list_available_datasets import list_available_datasets_task

        task = list_available_datasets_task.delay(source_filter)

        return {
            "task_id": task.id,
            "status": "started",
            "message": f"Listing datasets with filter: {source_filter}"
        }
    except Exception as e:
        logger.error(f"Error in list_available_datasets: {e}")
        return {"error": str(e)}

@mcp.tool()
def clean_data(dataset_info: Dict[str, Any], custom_actions: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
    """Clean and preprocess dataset"""
    try:
        from .tools.clean_data import clean_data_task

        task = clean_data_task.delay(dataset_info, custom_actions)

        return {
            "task_id": task.id,
            "status": "started",
            "message": f"Cleaning dataset: {dataset_info.get('name', 'unknown')}"
        }
    except Exception as e:
        logger.error(f"Error in clean_data: {e}")
        return {"error": str(e)}

@mcp.tool()
def detect_problem_type(dataset_path: str, target_column: Optional[str] = None) -> Dict[str, Any]:
    """Detect ML problem type from dataset"""
    try:
        from .tools.detect_problem_type import detect_problem_type_task

        task = detect_problem_type_task.delay(dataset_path, target_column)

        return {
            "task_id": task.id,
            "status": "started",
            "message": f"Detecting problem type for dataset: {dataset_path}"
        }
    except Exception as e:
        logger.error(f"Error in detect_problem_type: {e}")
        return {"error": str(e)}

@mcp.tool()
def train_multiple_models(dataset_path: str, problem_type: str, target_column: str, max_models: int = 5) -> Dict[str, Any]:
    """Train multiple ML models"""
    try:
        from .tools.train_multiple_models import train_multiple_models_task

        task = train_multiple_models_task.delay(dataset_path, problem_type, target_column, max_models)

        return {
            "task_id": task.id,
            "status": "started",
            "message": f"Training {max_models} models for {problem_type} problem"
        }
    except Exception as e:
        logger.error(f"Error in train_multiple_models: {e}")
        return {"error": str(e)}

@mcp.tool()
def evaluate_models(model_results: List[Dict[str, Any]], evaluation_metrics: Optional[List[str]] = None) -> Dict[str, Any]:
    """Evaluate trained models"""
    try:
        # For now, return a simple evaluation
        # In a full implementation, this would use a separate evaluation task

        if not model_results:
            return {"error": "No models to evaluate"}

        # Simple evaluation logic
        best_model = None
        best_score = -float('inf')

        for model in model_results:
            metrics = model.get('metrics', {})
            # Use accuracy for classification, r2 for regression
            score = metrics.get('accuracy', metrics.get('r2', 0))

            if score > best_score:
                best_score = score
                best_model = model

        return {
            "best_model": best_model,
            "all_models": model_results,
            "evaluation_complete": True,
            "message": f"Best model: {best_model.get('model_name', 'unknown')} with score: {best_score:.3f}"
        }
    except Exception as e:
        logger.error(f"Error in evaluate_models: {e}")
        return {"error": str(e)}

@mcp.tool()
def start_pipeline(user_request: str) -> Dict[str, Any]:
    """Start a new data science pipeline session"""
    try:
        from ..client_agent.orchestrator import orchestrator

        session_id = orchestrator.start_pipeline(user_request)

        return {
            "session_id": session_id,
            "status": "started",
            "user_request": user_request,
            "message": f"Started new pipeline session: {session_id}"
        }
    except Exception as e:
        logger.error(f"Error in start_pipeline: {e}")
        return {"error": str(e)}

@mcp.tool()
def get_pipeline_status(session_id: str) -> Dict[str, Any]:
    """Get current pipeline status"""
    try:
        from ..client_agent.orchestrator import orchestrator

        status = orchestrator.get_pipeline_status(session_id)
        return status
    except Exception as e:
        logger.error(f"Error in get_pipeline_status: {e}")
        return {"error": str(e)}

@mcp.tool()
def chat_with_assistant(message: str, session_id: Optional[str] = None) -> Dict[str, Any]:
    """Chat with AI assistant about the pipeline"""
    try:
        from ..client_agent.llm_chat_agent import chat_agent

        # Build context
        context = {}
        if session_id:
            pipeline_context = pipeline_state.get_context(session_id)
            if pipeline_context:
                context = {
                    'session_id': session_id,
                    'current_step': pipeline_context.current_step.value,
                    'step_results': {
                        step_result['step']: step_result['result']
                        for step_result in pipeline_context.step_history
                    }
                }

        response = chat_agent.get_response(message, context)

        return {
            "response": response,
            "session_id": session_id,
            "message_processed": True
        }
    except Exception as e:
        logger.error(f"Error in chat_with_assistant: {e}")
        return {"error": str(e)}

@mcp.tool()
def get_task_result(task_id: str) -> Dict[str, Any]:
    """Get result of a Celery task"""
    try:
        from ..client_agent.orchestrator import orchestrator

        result = orchestrator.get_task_result(task_id)
        return result
    except Exception as e:
        logger.error(f"Error in get_task_result: {e}")
        return {"error": str(e)}

@mcp.tool()
def execute_pipeline_step(session_id: str, step: str, **kwargs) -> Dict[str, Any]:
    """Execute a specific pipeline step"""
    try:
        from ..client_agent.orchestrator import orchestrator

        # Convert string to PipelineStep enum
        step_enum = PipelineStep(step)

        # Execute step
        task_id = orchestrator.execute_step(session_id, step_enum, **kwargs)

        return {
            "task_id": task_id,
            "step": step,
            "session_id": session_id,
            "status": "started",
            "message": f"Executing step: {step}"
        }
    except Exception as e:
        logger.error(f"Error in execute_pipeline_step: {e}")
        return {"error": str(e)}

# Server startup function
async def run_mcp_server():
    """Run the MCP server"""
    try:
        logger.info("Starting MCP server...")
        await mcp.run()
    except Exception as e:
        logger.error(f"Error running MCP server: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(run_mcp_server())