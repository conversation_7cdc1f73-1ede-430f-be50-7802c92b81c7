"""
MCP Server - FastAPI server that ties all tools together
"""
import logging
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import uuid

from .models import PipelineStep, DatasetInfo, pipeline_state
from .celery import celery_app
from ..config import config

logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="AI Data Science Pipeline API",
    description="MCP Server for the AI Data Science Pipeline",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API
class StartPipelineRequest(BaseModel):
    user_request: str

class ExecuteStepRequest(BaseModel):
    session_id: str
    step: str
    parameters: Optional[Dict[str, Any]] = None

class UserFeedbackRequest(BaseModel):
    session_id: str
    step: str
    action: str  # approve, reject, modify
    feedback: Optional[str] = None
    modifications: Optional[Dict[str, Any]] = None

@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "AI Data Science Pipeline MCP Server", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check Celery worker health
        health_task = celery_app.send_task('predictive_ai.mcp_server.celery.health_check')
        result = health_task.get(timeout=5)

        return {
            "status": "healthy",
            "celery": "connected",
            "worker_health": result
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }

@app.post("/pipeline/start")
async def start_pipeline(request: StartPipelineRequest):
    """Start a new pipeline session"""
    try:
        from ..client_agent.orchestrator import orchestrator

        session_id = orchestrator.start_pipeline(request.user_request)

        return {
            "session_id": session_id,
            "status": "started",
            "user_request": request.user_request
        }
    except Exception as e:
        logger.error(f"Error starting pipeline: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/pipeline/{session_id}/status")
async def get_pipeline_status(session_id: str):
    """Get pipeline status"""
    try:
        from ..client_agent.orchestrator import orchestrator

        status = orchestrator.get_pipeline_status(session_id)
        return status
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting pipeline status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/pipeline/execute")
async def execute_step(request: ExecuteStepRequest):
    """Execute a pipeline step"""
    try:
        from ..client_agent.orchestrator import orchestrator

        # Convert string to PipelineStep enum
        step = PipelineStep(request.step)

        # Execute step
        task_id = orchestrator.execute_step(
            request.session_id,
            step,
            **(request.parameters or {})
        )

        return {
            "task_id": task_id,
            "step": request.step,
            "status": "started"
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error executing step: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/task/{task_id}/result")
async def get_task_result(task_id: str):
    """Get task result"""
    try:
        from ..client_agent.orchestrator import orchestrator

        result = orchestrator.get_task_result(task_id)
        return result
    except Exception as e:
        logger.error(f"Error getting task result: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/pipeline/feedback")
async def handle_user_feedback(request: UserFeedbackRequest):
    """Handle user feedback"""
    try:
        from ..client_agent.orchestrator import orchestrator

        # Convert string to PipelineStep enum
        step = PipelineStep(request.step)

        result = orchestrator.handle_user_feedback(
            request.session_id,
            step,
            request.action,
            feedback=request.feedback,
            modifications=request.modifications
        )

        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error handling feedback: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/datasets/suggest")
async def suggest_datasets(user_request: str, max_suggestions: int = 5):
    """Suggest datasets based on user request"""
    try:
        from .tools.suggest_datasets import suggest_datasets_task

        task = suggest_datasets_task.delay(user_request, max_suggestions)

        return {
            "task_id": task.id,
            "status": "started"
        }
    except Exception as e:
        logger.error(f"Error suggesting datasets: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/datasets/list")
async def list_datasets(source_filter: Optional[str] = None):
    """List all available datasets"""
    try:
        from .tools.list_available_datasets import list_available_datasets_task

        task = list_available_datasets_task.delay(source_filter)

        return {
            "task_id": task.id,
            "status": "started"
        }
    except Exception as e:
        logger.error(f"Error listing datasets: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat")
async def chat_with_assistant(message: str, session_id: Optional[str] = None):
    """Chat with the AI assistant"""
    try:
        from ..client_agent.llm_chat_agent import chat_agent

        # Build context
        context = {}
        if session_id:
            pipeline_context = pipeline_state.get_context(session_id)
            if pipeline_context:
                context = {
                    'session_id': session_id,
                    'current_step': pipeline_context.current_step.value,
                    'step_results': {
                        step_result['step']: step_result['result']
                        for step_result in pipeline_context.step_history
                    }
                }

        response = chat_agent.get_response(message, context)

        return {
            "response": response,
            "session_id": session_id
        }
    except Exception as e:
        logger.error(f"Error in chat: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/sessions")
async def list_active_sessions():
    """List all active pipeline sessions"""
    try:
        sessions = pipeline_state.list_active_sessions()
        return {"active_sessions": sessions}
    except Exception as e:
        logger.error(f"Error listing sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """Delete a pipeline session"""
    try:
        pipeline_state.delete_context(session_id)
        return {"message": f"Session {session_id} deleted"}
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)