"""
Data models for the AI Data Science Pipeline
"""
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import json
from datetime import datetime
import pandas as pd

class PipelineStep(Enum):
    """Pipeline step enumeration"""
    DATASET_DISCOVERY = "dataset_discovery"
    DATA_CLEANING = "data_cleaning"
    PROBLEM_DETECTION = "problem_detection"
    MODEL_TRAINING = "model_training"
    MODEL_EVALUATION = "model_evaluation"
    HYPERPARAMETER_TUNING = "hyperparameter_tuning"
    FINAL_MODEL_SAVING = "final_model_saving"

class ProblemType(Enum):
    """Problem type enumeration"""
    CLASSIFICATION = "classification"
    REGRESSION = "regression"
    CLUSTERING = "clustering"
    TIME_SERIES = "time_series"
    ANOMALY_DETECTION = "anomaly_detection"

class TaskStatus(Enum):
    """Task status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class DatasetInfo:
    """Dataset information model"""
    name: str
    path: str
    source: str  # 'file' or 'database'
    size_mb: float
    rows: int
    columns: int
    description: Optional[str] = None
    column_info: Optional[Dict[str, Any]] = None
    missing_values: Optional[Dict[str, int]] = None
    data_types: Optional[Dict[str, str]] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'path': self.path,
            'source': self.source,
            'size_mb': self.size_mb,
            'rows': self.rows,
            'columns': self.columns,
            'description': self.description,
            'column_info': self.column_info,
            'missing_values': self.missing_values,
            'data_types': self.data_types
        }

@dataclass
class CleaningAction:
    """Data cleaning action model"""
    action_type: str  # 'drop_column', 'fill_missing', 'remove_outliers', etc.
    column: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    reason: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            'action_type': self.action_type,
            'column': self.column,
            'parameters': self.parameters,
            'reason': self.reason
        }

@dataclass
class CleaningResult:
    """Data cleaning result model"""
    original_shape: tuple
    cleaned_shape: tuple
    actions_performed: List[CleaningAction]
    removed_columns: List[str]
    filled_missing: Dict[str, Any]
    outliers_removed: int
    summary: str

    def to_dict(self) -> Dict[str, Any]:
        return {
            'original_shape': self.original_shape,
            'cleaned_shape': self.cleaned_shape,
            'actions_performed': [action.to_dict() for action in self.actions_performed],
            'removed_columns': self.removed_columns,
            'filled_missing': self.filled_missing,
            'outliers_removed': self.outliers_removed,
            'summary': self.summary
        }

@dataclass
class ModelConfig:
    """Model configuration"""
    name: str
    algorithm: str
    hyperparameters: Dict[str, Any]
    problem_type: ProblemType

    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'algorithm': self.algorithm,
            'hyperparameters': self.hyperparameters,
            'problem_type': self.problem_type.value
        }

@dataclass
class ModelResult:
    """Model training/evaluation result"""
    model_name: str
    algorithm: str
    problem_type: ProblemType
    metrics: Dict[str, float]
    training_time: float
    model_path: Optional[str] = None
    feature_importance: Optional[Dict[str, float]] = None
    confusion_matrix: Optional[List[List[int]]] = None
    predictions: Optional[List[Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            'model_name': self.model_name,
            'algorithm': self.algorithm,
            'problem_type': self.problem_type.value,
            'metrics': self.metrics,
            'training_time': self.training_time,
            'model_path': self.model_path,
            'feature_importance': self.feature_importance,
            'confusion_matrix': self.confusion_matrix,
            'predictions': self.predictions
        }

@dataclass
class PipelineContext:
    """Pipeline execution context"""
    session_id: str
    user_request: str
    current_step: PipelineStep
    step_history: List[Dict[str, Any]] = field(default_factory=list)
    dataset_info: Optional[DatasetInfo] = None
    cleaning_result: Optional[CleaningResult] = None
    problem_type: Optional[ProblemType] = None
    target_column: Optional[str] = None
    feature_columns: Optional[List[str]] = None
    model_results: List[ModelResult] = field(default_factory=list)
    best_model: Optional[ModelResult] = None
    user_preferences: Dict[str, Any] = field(default_factory=dict)

    def add_step_result(self, step: PipelineStep, result: Dict[str, Any], status: TaskStatus = TaskStatus.SUCCESS):
        """Add a step result to history"""
        self.step_history.append({
            'step': step.value,
            'timestamp': datetime.now().isoformat(),
            'status': status.value,
            'result': result
        })

    def get_step_result(self, step: PipelineStep) -> Optional[Dict[str, Any]]:
        """Get the result of a specific step"""
        for step_result in reversed(self.step_history):
            if step_result['step'] == step.value:
                return step_result['result']
        return None

    def to_dict(self) -> Dict[str, Any]:
        return {
            'session_id': self.session_id,
            'user_request': self.user_request,
            'current_step': self.current_step.value,
            'step_history': self.step_history,
            'dataset_info': self.dataset_info.to_dict() if self.dataset_info else None,
            'cleaning_result': self.cleaning_result.to_dict() if self.cleaning_result else None,
            'problem_type': self.problem_type.value if self.problem_type else None,
            'target_column': self.target_column,
            'feature_columns': self.feature_columns,
            'model_results': [result.to_dict() for result in self.model_results],
            'best_model': self.best_model.to_dict() if self.best_model else None,
            'user_preferences': self.user_preferences
        }

@dataclass
class TaskResult:
    """Generic task result model"""
    task_id: str
    status: TaskStatus
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            'task_id': self.task_id,
            'status': self.status.value,
            'result': self.result,
            'error': self.error,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }

@dataclass
class UserFeedback:
    """User feedback model"""
    session_id: str
    step: PipelineStep
    action: str  # 'approve', 'reject', 'modify'
    feedback: Optional[str] = None
    modifications: Optional[Dict[str, Any]] = None
    timestamp: datetime = field(default_factory=datetime.now)

    def to_dict(self) -> Dict[str, Any]:
        return {
            'session_id': self.session_id,
            'step': self.step.value,
            'action': self.action,
            'feedback': self.feedback,
            'modifications': self.modifications,
            'timestamp': self.timestamp.isoformat()
        }

@dataclass
class InsightResult:
    """Data insight result model"""
    insight_type: str  # 'statistical', 'visual', 'correlation', etc.
    title: str
    description: str
    data: Dict[str, Any]
    visualization: Optional[str] = None  # Base64 encoded plot or chart config

    def to_dict(self) -> Dict[str, Any]:
        return {
            'insight_type': self.insight_type,
            'title': self.title,
            'description': self.description,
            'data': self.data,
            'visualization': self.visualization
        }

class PipelineStateManager:
    """Manages pipeline state across sessions"""

    def __init__(self):
        self._contexts: Dict[str, PipelineContext] = {}

    def create_context(self, session_id: str, user_request: str) -> PipelineContext:
        """Create a new pipeline context"""
        context = PipelineContext(
            session_id=session_id,
            user_request=user_request,
            current_step=PipelineStep.DATASET_DISCOVERY
        )
        self._contexts[session_id] = context
        return context

    def get_context(self, session_id: str) -> Optional[PipelineContext]:
        """Get pipeline context by session ID"""
        return self._contexts.get(session_id)

    def update_context(self, session_id: str, context: PipelineContext):
        """Update pipeline context"""
        self._contexts[session_id] = context

    def delete_context(self, session_id: str):
        """Delete pipeline context"""
        if session_id in self._contexts:
            del self._contexts[session_id]

    def list_active_sessions(self) -> List[str]:
        """List all active session IDs"""
        return list(self._contexts.keys())

# Global state manager instance
pipeline_state = PipelineStateManager()