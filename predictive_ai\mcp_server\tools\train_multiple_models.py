"""
Multiple model training tool - trains multiple ML models
"""
import logging
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.metrics import accuracy_score, mean_squared_error, r2_score
import time
import pickle
from typing import List, Dict, Any
from pathlib import Path

from ..celery import celery_app
from ..models import ModelResult, ProblemType
from ...config import config

logger = logging.getLogger(__name__)

class MultiModelTrainer:
    """Trains multiple ML models for comparison"""

    def __init__(self):
        self.config = config

    def get_models_for_problem_type(self, problem_type: str) -> Dict[str, Any]:
        """Get appropriate models for the problem type"""
        if problem_type == ProblemType.CLASSIFICATION.value:
            return {
                'RandomForest': RandomForestClassifier(random_state=self.config.ml.random_state),
                'LogisticRegression': LogisticRegression(random_state=self.config.ml.random_state, max_iter=1000)
            }
        elif problem_type == ProblemType.REGRESSION.value:
            return {
                'RandomForest': RandomForestRegressor(random_state=self.config.ml.random_state),
                'LinearRegression': LinearRegression()
            }
        else:
            # Default to classification models
            return {
                'RandomForest': RandomForestClassifier(random_state=self.config.ml.random_state)
            }

    def prepare_data(self, df: pd.DataFrame, target_column: str):
        """Prepare data for training"""
        # Separate features and target
        X = df.drop(columns=[target_column])
        y = df[target_column]

        # Handle categorical variables (simple encoding)
        for col in X.select_dtypes(include=['object']).columns:
            X[col] = pd.Categorical(X[col]).codes

        # Handle missing values
        X = X.fillna(X.mean() if X.select_dtypes(include=[np.number]).shape[1] > 0 else 0)
        y = y.fillna(y.mean() if pd.api.types.is_numeric_dtype(y) else y.mode().iloc[0])

        return X, y