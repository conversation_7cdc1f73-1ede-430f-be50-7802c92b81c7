[project]
name = "predictive-ai"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "anyio>=4.0.0",
    "black>=23.0.0",
    "celery>=5.3.0",
    "fastapi>=0.104.0",
    "fastmcp>=0.9.0",
    "flake8>=6.0.0",
    "httpx>=0.25.0",
    "langchain>=0.0.350",
    "lightgbm>=4.0.0",
    "matplotlib>=3.7.0",
    "mcp>=1.0.0",
    "numpy>=1.24.0",
    "openai>=1.3.0",
    "openpyxl>=3.1.0",
    "optuna>=3.4.0",
    "pandas>=2.0.0",
    "plotly>=5.17.0",
    "psycopg2-binary>=2.9.0",
    "pydantic>=2.5.0",
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0",
    "redis>=4.5.0",
    "scikit-learn>=1.3.0",
    "seaborn>=0.12.0",
    "sqlalchemy>=2.0.0",
    "streamlit>=1.28.0",
    "structlog>=23.2.0",
    "typing-extensions>=4.8.0",
    "uvicorn>=0.24.0",
    "xgboost>=1.7.0",
    "xlrd>=2.0.0",
]
