"""
List available datasets tool - provides fallback when suggestions are rejected
"""
import logging
from typing import List, Dict, Any
from ..celery import celery_app
from ..models import DatasetInfo
from .suggest_datasets import DatasetSuggester

logger = logging.getLogger(__name__)

class DatasetLister:
    """Lists all available datasets for manual selection"""

    def __init__(self):
        self.suggester = DatasetSuggester()

    def list_all_datasets(self, source_filter: str = None) -> List[DatasetInfo]:
        """List all available datasets with optional source filtering"""
        logger.info(f"Listing all datasets with filter: {source_filter}")

        all_datasets = []

        if source_filter is None or source_filter == "file":
            all_datasets.extend(self.suggester._get_file_datasets())

        if source_filter is None or source_filter == "database":
            all_datasets.extend(self.suggester._get_database_datasets())

        # Sort by name for consistent ordering
        all_datasets.sort(key=lambda x: x.name.lower())

        logger.info(f"Found {len(all_datasets)} total datasets")
        return all_datasets

    def get_dataset_details(self, dataset_name: str) -> DatasetInfo:
        """Get detailed information about a specific dataset"""
        logger.info(f"Getting details for dataset: {dataset_name}")

        all_datasets = self.list_all_datasets()

        for dataset in all_datasets:
            if dataset.name.lower() == dataset_name.lower():
                return dataset

        raise ValueError(f"Dataset '{dataset_name}' not found")

@celery_app.task(bind=True)
def list_available_datasets_task(self, source_filter: str = None):
    """Celery task to list all available datasets"""
    try:
        logger.info(f"Starting list datasets task with filter: {source_filter}")

        lister = DatasetLister()
        datasets = lister.list_all_datasets(source_filter)

        result = {
            'datasets': [dataset.to_dict() for dataset in datasets],
            'total_count': len(datasets),
            'source_filter': source_filter
        }

        logger.info(f"List datasets task completed successfully")
        return result

    except Exception as e:
        logger.error(f"List datasets task failed: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)

@celery_app.task(bind=True)
def get_dataset_details_task(self, dataset_name: str):
    """Celery task to get dataset details"""
    try:
        logger.info(f"Starting get dataset details task for: {dataset_name}")

        lister = DatasetLister()
        dataset = lister.get_dataset_details(dataset_name)

        result = {
            'dataset': dataset.to_dict(),
            'dataset_name': dataset_name
        }

        logger.info(f"Get dataset details task completed successfully")
        return result

    except Exception as e:
        logger.error(f"Get dataset details task failed: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)