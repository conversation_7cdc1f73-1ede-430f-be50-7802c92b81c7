"""
Celery configuration and task queue setup
"""
import os
import logging
from celery import Celery
from celery.signals import task_prerun, task_postrun, task_failure
from datetime import datetime

# Import configuration
from ..config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Celery app
celery_app = Celery(
    'predictive_ai_pipeline',
    broker=config.celery.broker_url,
    backend=config.celery.result_backend,
    include=[
        'predictive_ai.mcp_server.tools.suggest_datasets',
        'predictive_ai.mcp_server.tools.list_available_datasets',
        'predictive_ai.mcp_server.tools.clean_data',
        'predictive_ai.mcp_server.tools.detect_problem_type',
        'predictive_ai.mcp_server.tools.train_model',
        'predictive_ai.mcp_server.tools.train_multiple_models',
        'predictive_ai.mcp_server.tools.evaluate_model',
        'predictive_ai.mcp_server.tools.hyperparam_tune',
        'predictive_ai.mcp_server.tools.get_insights',
        'predictive_ai.mcp_server.tools.fallback_recommender'
    ]
)

# Configure Celery
celery_app.conf.update(
    task_serializer=config.celery.task_serializer,
    accept_content=config.celery.accept_content,
    result_serializer=config.celery.result_serializer,
    timezone=config.celery.timezone,
    enable_utc=config.celery.enable_utc,
    worker_concurrency=config.celery.worker_concurrency,
    task_routes={
        'predictive_ai.mcp_server.tools.*': {'queue': 'ml_tasks'},
    },
    task_annotations={
        '*': {'rate_limit': '10/s'}
    },
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=False,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_max_tasks_per_child=1000,
    worker_max_memory_per_child=200000,  # 200MB
)

# Task monitoring signals
@task_prerun.connect
def task_prerun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds):
    """Log task start"""
    logger.info(f"Task {task.name} [{task_id}] started at {datetime.now()}")

@task_postrun.connect
def task_postrun_handler(sender=None, task_id=None, task=None, args=None, kwargs=None, retval=None, state=None, **kwds):
    """Log task completion"""
    logger.info(f"Task {task.name} [{task_id}] completed with state: {state}")

@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwds):
    """Log task failure"""
    logger.error(f"Task {sender.name} [{task_id}] failed: {exception}")
    logger.error(f"Traceback: {traceback}")

# Health check task
@celery_app.task(bind=True)
def health_check(self):
    """Health check task for monitoring"""
    return {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'worker_id': self.request.id
    }

# Task result cleanup
@celery_app.task
def cleanup_old_results():
    """Clean up old task results"""
    try:
        # This would typically clean up old results from the backend
        # Implementation depends on the backend used (Redis, database, etc.)
        logger.info("Cleaning up old task results")
        return {"status": "completed", "message": "Old results cleaned up"}
    except Exception as e:
        logger.error(f"Failed to clean up old results: {e}")
        raise

if __name__ == '__main__':
    celery_app.start()