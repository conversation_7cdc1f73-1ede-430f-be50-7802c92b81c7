"""
Data cleaning tool - automatically cleans selected data
"""
import logging
import pandas as pd
import numpy as np
import sqlalchemy as sa
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import pickle

from ..celery import celery_app
from ..models import DatasetInfo, CleaningAction, CleaningResult
from ...config import config

logger = logging.getLogger(__name__)

class DataCleaner:
    """Handles automatic data cleaning operations"""

    def __init__(self):
        self.config = config

    def load_dataset(self, dataset_info: DatasetInfo) -> pd.DataFrame:
        """Load dataset from file or database"""
        logger.info(f"Loading dataset: {dataset_info.name}")

        if dataset_info.source == "file":
            file_path = Path(dataset_info.path)

            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            elif file_path.suffix.lower() == '.json':
                df = pd.read_json(file_path)
            else:
                raise ValueError(f"Unsupported file format: {file_path.suffix}")

        elif dataset_info.source == "database":
            table_name = dataset_info.name
            engine = sa.create_engine(self.config.database.connection_string)
            df = pd.read_sql_table(table_name, engine)
        else:
            raise ValueError(f"Unsupported data source: {dataset_info.source}")

        logger.info(f"Loaded dataset with shape: {df.shape}")
        return df