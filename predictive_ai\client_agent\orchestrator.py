"""
Pipeline Orchestrator - Controls the entire ML pipeline flow
"""
import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
import asyncio

from ..mcp_server.models import (
    PipelineStep, PipelineContext, ProblemType, TaskStatus,
    DatasetInfo, pipeline_state
)
from ..mcp_server.celery import celery_app
from ..config import config

logger = logging.getLogger(__name__)

class PipelineOrchestrator:
    """Orchestrates the entire data science pipeline"""

    def __init__(self):
        self.config = config
        self.active_tasks = {}

    def start_pipeline(self, user_request: str) -> str:
        """Start a new pipeline session"""
        session_id = str(uuid.uuid4())

        # Create pipeline context
        context = pipeline_state.create_context(session_id, user_request)

        logger.info(f"Started new pipeline session: {session_id}")
        logger.info(f"User request: {user_request}")

        return session_id

    def get_pipeline_status(self, session_id: str) -> Dict[str, Any]:
        """Get current pipeline status"""
        context = pipeline_state.get_context(session_id)
        if not context:
            raise ValueError(f"Session {session_id} not found")

        return {
            'session_id': session_id,
            'current_step': context.current_step.value,
            'progress': len(context.step_history),
            'status': 'active',
            'last_updated': datetime.now().isoformat()
        }

    def execute_step(self, session_id: str, step: PipelineStep, **kwargs) -> str:
        """Execute a specific pipeline step"""
        context = pipeline_state.get_context(session_id)
        if not context:
            raise ValueError(f"Session {session_id} not found")

        logger.info(f"Executing step {step.value} for session {session_id}")

        # Update current step
        context.current_step = step
        pipeline_state.update_context(session_id, context)

        # Execute the appropriate task based on step
        if step == PipelineStep.DATASET_DISCOVERY:
            return self._execute_dataset_discovery(session_id, **kwargs)
        elif step == PipelineStep.DATA_CLEANING:
            return self._execute_data_cleaning(session_id, **kwargs)
        elif step == PipelineStep.PROBLEM_DETECTION:
            return self._execute_problem_detection(session_id, **kwargs)
        elif step == PipelineStep.MODEL_TRAINING:
            return self._execute_model_training(session_id, **kwargs)
        elif step == PipelineStep.MODEL_EVALUATION:
            return self._execute_model_evaluation(session_id, **kwargs)
        elif step == PipelineStep.HYPERPARAMETER_TUNING:
            return self._execute_hyperparameter_tuning(session_id, **kwargs)
        else:
            raise ValueError(f"Unknown step: {step}")

    def _execute_dataset_discovery(self, session_id: str, **kwargs) -> str:
        """Execute dataset discovery step"""
        from ..mcp_server.tools.suggest_datasets import suggest_datasets_task

        context = pipeline_state.get_context(session_id)
        user_request = context.user_request

        # Start async task
        task = suggest_datasets_task.delay(
            user_request=user_request,
            max_suggestions=kwargs.get('max_suggestions', 5)
        )

        self.active_tasks[session_id] = task.id
        logger.info(f"Started dataset discovery task: {task.id}")

        return task.id