# AI Data Science Pipeline Configuration

# Database Configuration
database:
  host: localhost
  port: 5432
  database: predictive_ai
  username: postgres
  password: your_password_here

# Celery Configuration
celery:
  broker_url: redis://localhost:6379/0
  result_backend: redis://localhost:6379/0
  worker_concurrency: 3
  task_serializer: json
  result_serializer: json
  timezone: UTC
  enable_utc: true

# Machine Learning Configuration
ml:
  max_models_per_problem: 5
  default_test_size: 0.2
  random_state: 42
  cv_folds: 5
  hyperparameter_trials: 50
  model_timeout_seconds: 300
  
  # Model configurations by problem type
  classification_models:
    - RandomForestClassifier
    - LogisticRegression
    - XGBClassifier
    - SVMClassifier
    - GradientBoostingClassifier
  
  regression_models:
    - RandomForestRegressor
    - LinearRegression
    - XGBRegressor
    - SVMRegressor
    - GradientBoostingRegressor
  
  clustering_models:
    - KMeans
    - DBSCAN
    - AgglomerativeClustering
    - GaussianMixture

# LLM Configuration
llm:
  model_name: gpt-3.5-turbo
  api_key: your_openai_api_key_here
  max_tokens: 1000
  temperature: 0.7
  system_prompt: |
    You are an AI Data Science Assistant. You help users understand each step 
    of the data science pipeline, explain recommendations, and provide insights 
    about their data and models.

# UI Configuration
ui:
  streamlit_port: 8501
  max_file_size_mb: 200
  allowed_file_types:
    - .csv
    - .xlsx
    - .xls
    - .txt
    - .json

# Logging Configuration
logging:
  level: INFO
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file: logs/app.log
