# AI Data Science Pipeline Configuration
# Note: Secrets (passwords, API keys) are loaded from .env file

# Database Configuration (non-sensitive settings only)
database:
  # Connection settings loaded from .env: DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD
  connection_timeout: 30
  pool_size: 5
  max_overflow: 10

# Celery Configuration (non-sensitive settings only)
celery:
  # Broker/backend URLs loaded from .env: CELERY_BROKER_URL, CELERY_RESULT_BACKEND
  task_serializer: json
  result_serializer: json
  timezone: UTC
  enable_utc: true
  task_time_limit: 1800  # 30 minutes
  task_soft_time_limit: 1500  # 25 minutes
  worker_prefetch_multiplier: 1
  task_acks_late: true

# Machine Learning Configuration
ml:
  max_models_per_problem: 5
  default_test_size: 0.2
  random_state: 42
  cv_folds: 5
  hyperparameter_trials: 50
  model_timeout_seconds: 300

  # Model configurations by problem type
  classification_models:
    - RandomForestClassifier
    - LogisticRegression
    - XGBClassifier
    - SVMClassifier
    - GradientBoostingClassifier

  regression_models:
    - RandomForestRegressor
    - LinearRegression
    - XGBRegressor
    - SVMRegressor
    - GradientBoostingRegressor

  clustering_models:
    - KMeans
    - DBSCAN
    - AgglomerativeClustering
    - GaussianMixture

# LLM Configuration (non-sensitive settings only)
llm:
  # API key loaded from .env: OPENAI_API_KEY
  # Model name loaded from .env: LLM_MODEL
  temperature: 0.7
  system_prompt: |
    You are an AI Data Science Assistant. You help users understand each step
    of the data science pipeline, explain recommendations, and provide insights
    about their data and models.

# UI Configuration
ui:
  # Port loaded from .env: STREAMLIT_PORT
  allowed_file_types:
    - .csv
    - .xlsx
    - .xls
    - .txt
    - .json
  upload_progress_bar: true
  auto_refresh_interval: 2  # seconds

# Logging Configuration
logging:
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file: logs/app.log
  max_file_size_mb: 10
  backup_count: 5
